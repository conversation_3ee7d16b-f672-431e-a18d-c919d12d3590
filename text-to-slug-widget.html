<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text to Slug Converter Widget</title>
    
    <!-- Widget Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "Text to Slug Converter Widget",
        "description": "Convert any text to URL-friendly slug format with customizable options",
        "applicationCategory": "WebApplication",
        "operatingSystem": "Any",
        "permissions": "browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "featureList": [
            "Convert text to URL-friendly slugs",
            "Remove special characters",
            "Customizable conversion options",
            "One-click copy to clipboard",
            "Real-time conversion",
            "Mobile responsive design"
        ]
    }
    </script>

    <style>
        /* Text to Slug Widget Styles - Namespaced to avoid conflicts */
        .text-slug-widget-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .text-slug-widget-container * {
            box-sizing: border-box;
        }

        .text-slug-widget-title {
            color: #1a202c;
            text-align: center;
            margin-bottom: 0.5rem;
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .text-slug-widget-description {
            text-align: center;
            color: #718096;
            margin-bottom: 2rem;
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .text-slug-input-group {
            margin-bottom: 1.5rem;
        }

        .text-slug-label {
            display: block;
            margin-bottom: 0.75rem;
            font-weight: 600;
            color: #2d3748;
            font-size: 1rem;
        }

        .text-slug-textarea {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            resize: vertical;
            min-height: 120px;
            background: #f7fafc;
            font-family: inherit;
        }

        .text-slug-textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: #ffffff;
        }

        .text-slug-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border-radius: 12px;
            border: 1px solid #e2e8f0;
        }

        .text-slug-option {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .text-slug-checkbox {
            width: 20px;
            height: 20px;
            accent-color: #667eea;
            cursor: pointer;
        }

        .text-slug-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: #2d3748;
            font-size: 0.95rem;
        }

        .text-slug-buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .text-slug-btn {
            padding: 0.875rem 1.75rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            flex: 1;
            min-width: 140px;
            position: relative;
            overflow: hidden;
        }

        .text-slug-btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .text-slug-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .text-slug-btn-secondary {
            background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
            color: white;
        }

        .text-slug-btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(160, 174, 192, 0.3);
        }

        .text-slug-btn-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .text-slug-btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
        }

        .text-slug-result {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border-radius: 12px;
            padding: 1.5rem;
            border-left: 4px solid #667eea;
            border: 1px solid #e2e8f0;
        }

        .text-slug-result-title {
            margin: 0 0 1rem 0;
            color: #2d3748;
            font-size: 1.25rem;
            font-weight: 700;
        }

        .text-slug-output {
            background: #ffffff;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem 1.25rem;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 1rem;
            word-break: break-all;
            min-height: 60px;
            color: #2d3748;
            line-height: 1.5;
            position: relative;
        }

        .text-slug-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
        }

        .text-slug-notification.show {
            transform: translateX(0);
        }

        .text-slug-features {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e2e8f0;
        }

        .text-slug-features-title {
            color: #2d3748;
            margin-bottom: 1rem;
            font-size: 1.25rem;
            font-weight: 700;
        }

        .text-slug-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .text-slug-features-item {
            padding: 0.5rem 0;
            color: #4a5568;
            position: relative;
            padding-left: 1.5rem;
        }

        .text-slug-features-item:before {
            content: "✓";
            color: #48bb78;
            font-weight: bold;
            position: absolute;
            left: 0;
            top: 0.5rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .text-slug-widget-container {
                margin: 1rem;
                padding: 1.5rem;
            }

            .text-slug-widget-title {
                font-size: 1.875rem;
            }

            .text-slug-buttons {
                flex-direction: column;
            }

            .text-slug-btn {
                flex: none;
            }

            .text-slug-options {
                grid-template-columns: 1fr;
            }
        }

        /* Dark Mode Support */
        @media (prefers-color-scheme: dark) {
            .text-slug-widget-container {
                background: #1a202c;
                border-color: #2d3748;
            }

            .text-slug-widget-title {
                background: linear-gradient(135deg, #90cdf4 0%, #a78bfa 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            .text-slug-widget-description {
                color: #a0aec0;
            }

            .text-slug-label,
            .text-slug-option-label,
            .text-slug-result-title,
            .text-slug-features-title {
                color: #e2e8f0;
            }

            .text-slug-textarea {
                background: #2d3748;
                border-color: #4a5568;
                color: #e2e8f0;
            }

            .text-slug-textarea:focus {
                background: #2d3748;
                border-color: #90cdf4;
            }

            .text-slug-options,
            .text-slug-result {
                background: #2d3748;
                border-color: #4a5568;
            }

            .text-slug-output {
                background: #1a202c;
                border-color: #4a5568;
                color: #e2e8f0;
            }

            .text-slug-features-item {
                color: #a0aec0;
            }
        }
    </style>
</head>
<body>
    <div class="text-slug-widget-container">
        <h1 class="text-slug-widget-title">Text to Slug Converter</h1>
        <p class="text-slug-widget-description">
            Transform any text into SEO-friendly, URL-safe slugs instantly. Perfect for creating clean URLs, file names, and identifiers.
        </p>
        
        <div class="text-slug-input-group">
            <label for="textSlugInput" class="text-slug-label">Enter your text:</label>
            <textarea 
                id="textSlugInput" 
                class="text-slug-textarea"
                placeholder="Type or paste your text here to convert it to a slug..."
                rows="4"
            ></textarea>
        </div>

        <div class="text-slug-options">
            <div class="text-slug-option">
                <input type="checkbox" id="slugLowercase" class="text-slug-checkbox" checked>
                <label for="slugLowercase" class="text-slug-option-label">Convert to lowercase</label>
            </div>
            <div class="text-slug-option">
                <input type="checkbox" id="slugRemoveSpecial" class="text-slug-checkbox" checked>
                <label for="slugRemoveSpecial" class="text-slug-option-label">Remove special characters</label>
            </div>
            <div class="text-slug-option">
                <input type="checkbox" id="slugTrimSpaces" class="text-slug-checkbox" checked>
                <label for="slugTrimSpaces" class="text-slug-option-label">Trim extra spaces</label>
            </div>
            <div class="text-slug-option">
                <input type="checkbox" id="slugRemoveNumbers" class="text-slug-checkbox">
                <label for="slugRemoveNumbers" class="text-slug-option-label">Remove numbers</label>
            </div>
        </div>

        <div class="text-slug-buttons">
            <button class="text-slug-btn text-slug-btn-primary" onclick="TextSlugConverter.convert()">
                Convert to Slug
            </button>
            <button class="text-slug-btn text-slug-btn-secondary" onclick="TextSlugConverter.clear()">
                Clear All
            </button>
            <button class="text-slug-btn text-slug-btn-success" onclick="TextSlugConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="text-slug-result">
            <h3 class="text-slug-result-title">Generated Slug:</h3>
            <div class="text-slug-output" id="textSlugOutput">
                Your converted slug will appear here...
            </div>
        </div>

        <div class="text-slug-features">
            <h3 class="text-slug-features-title">Key Features:</h3>
            <ul class="text-slug-features-list">
                <li class="text-slug-features-item">Instant text-to-slug conversion</li>
                <li class="text-slug-features-item">Customizable conversion options</li>
                <li class="text-slug-features-item">SEO-friendly URL generation</li>
                <li class="text-slug-features-item">One-click copy to clipboard</li>
                <li class="text-slug-features-item">Real-time preview</li>
                <li class="text-slug-features-item">Mobile-responsive design</li>
                <li class="text-slug-features-item">No data sent to servers</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="text-slug-notification" id="textSlugNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Text to Slug Converter Widget - IIFE Pattern to avoid conflicts
        (function() {
            'use strict';

            // Create isolated namespace
            window.TextSlugConverter = {

                convert: function() {
                    const input = document.getElementById('textSlugInput');
                    const output = document.getElementById('textSlugOutput');
                    const text = input.value;

                    if (!text.trim()) {
                        output.textContent = 'Please enter some text to convert.';
                        output.style.color = '#e53e3e';
                        return;
                    }

                    // Reset output color
                    output.style.color = '';

                    // Get conversion options
                    const options = {
                        lowercase: document.getElementById('slugLowercase').checked,
                        removeSpecial: document.getElementById('slugRemoveSpecial').checked,
                        trimSpaces: document.getElementById('slugTrimSpaces').checked,
                        removeNumbers: document.getElementById('slugRemoveNumbers').checked
                    };

                    // Convert text to slug
                    const slug = this.processText(text, options);
                    output.textContent = slug || 'No valid characters found';
                },

                processText: function(text, options) {
                    let result = text;

                    // Apply lowercase conversion
                    if (options.lowercase) {
                        result = result.toLowerCase();
                    }

                    // Remove numbers if requested
                    if (options.removeNumbers) {
                        result = result.replace(/\d+/g, '');
                    }

                    // Remove special characters but preserve spaces and hyphens
                    if (options.removeSpecial) {
                        result = result.replace(/[^\w\s-]/g, '');
                    }

                    // Trim and normalize spaces
                    if (options.trimSpaces) {
                        result = result.replace(/\s+/g, ' ').trim();
                    }

                    // Convert spaces to hyphens
                    result = result.replace(/\s+/g, '-');

                    // Remove multiple consecutive hyphens
                    result = result.replace(/-+/g, '-');

                    // Remove leading and trailing hyphens
                    result = result.replace(/^-+|-+$/g, '');

                    return result;
                },

                clear: function() {
                    document.getElementById('textSlugInput').value = '';
                    document.getElementById('textSlugOutput').textContent = 'Your converted slug will appear here...';
                    document.getElementById('textSlugOutput').style.color = '';
                },

                copy: function() {
                    const output = document.getElementById('textSlugOutput');
                    const text = output.textContent;

                    // Don't copy placeholder or error messages
                    if (text === 'Your converted slug will appear here...' ||
                        text === 'Please enter some text to convert.' ||
                        text === 'No valid characters found') {
                        return;
                    }

                    // Modern clipboard API with fallback
                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => {
                            this.showNotification();
                        }).catch(() => {
                            this.fallbackCopy(text);
                        });
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy: function(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.position = 'fixed';
                    textArea.style.left = '-999999px';
                    textArea.style.top = '-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();

                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }

                    document.body.removeChild(textArea);
                },

                showNotification: function() {
                    const notification = document.getElementById('textSlugNotification');
                    notification.classList.add('show');
                    setTimeout(() => {
                        notification.classList.remove('show');
                    }, 2500);
                }
            };

            // Initialize widget when DOM is ready
            document.addEventListener('DOMContentLoaded', function() {
                const input = document.getElementById('textSlugInput');
                const checkboxes = document.querySelectorAll('.text-slug-checkbox');

                // Auto-convert on input
                input.addEventListener('input', function() {
                    if (this.value.trim()) {
                        TextSlugConverter.convert();
                    } else {
                        document.getElementById('textSlugOutput').textContent = 'Your converted slug will appear here...';
                        document.getElementById('textSlugOutput').style.color = '';
                    }
                });

                // Auto-convert when options change
                checkboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        const inputValue = document.getElementById('textSlugInput').value;
                        if (inputValue.trim()) {
                            TextSlugConverter.convert();
                        }
                    });
                });

                // Handle Enter key in textarea
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        TextSlugConverter.convert();
                    }
                });
            });

        })();
    </script>
</body>
</html>
